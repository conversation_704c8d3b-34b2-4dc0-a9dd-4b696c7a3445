# Be sure to restart your server when you modify this file.

# Version of your assets, change this if you want to expire all your assets.
Rails.application.config.assets.version = '1.0'

# Add additional assets to the asset load path.
# Rails.application.config.assets.paths << Emoji.images_path

# Precompile additional assets.
# application.js, application.css, and all non-JS/CSS in the app/assets
# folder are already added.
# Rails.application.config.assets.precompile += %w( admin.js admin.css )

# Add house prices assets to precompile list
Rails.application.config.assets.precompile += %w[house_prices.js house_prices.css]

# https://github.com/thoughtbot/administrate/pull/2397
# hoping below will fix issue with administrate conflicting with sassc-rails
# gem which is needed for tailwind
Rails.application.config.assets.css_compressor = nil

# Remove SassC processor to avoid sassc dependency
Rails.application.config.assets.configure do |env|
  env.unregister_processor('text/scss', Sprockets::SasscProcessor) if defined?(Sprockets::SasscProcessor)
  env.unregister_processor('text/sass', Sprockets::SasscProcessor) if defined?(Sprockets::SasscProcessor)
end
