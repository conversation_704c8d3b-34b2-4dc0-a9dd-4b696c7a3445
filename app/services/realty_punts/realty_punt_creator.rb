require 'logger'
require 'httparty'
require 'json'

module RealtyPunts
  # 16 june 2025: the idea of this class is to call to the backend on the server
  # so that methods can be called from my local desktop but the data
  # gets created on the server
  # Service class to handle dossier and asset creation
  class RealtyPuntCreator
    def initialize(api_prefix = nil, logger = Logger.new(STDOUT))
      @api_prefix = api_prefix
      @logger = logger
    end

    def create_realty_game(input_file = 'db/realty_punts/latest.json')
      input_data = read_and_validate_input(input_file)
      property_urls = input_data['property_urls'] || []
      validate_property_urls(property_urls)

      # puts '🔧 Starting realty game creation'"
      # puts "🔸 Using retrieval portal: #{input_data['retrieval_portal']}"
      # puts "🔸 Found #{property_urls.size} property URL(s)"

      realty_game_id = create_initial_game(input_data, property_urls.first)
      add_remaining_listings(input_data, property_urls.drop(1), realty_game_id)

      puts "🎯 All listings processed. Realty game ID: #{realty_game_id}"
      realty_game_id
    end

    def create_realty_game_with_pre_scraped_content(input_file = 'db/realty_punts/latest.json')
      input_data = read_and_validate_input(input_file)
      property_urls = input_data['property_urls'] || []
      validate_property_urls(property_urls)

      puts '🔧 Starting realty game creation with pre-scraped content'
      # puts "🔸 Using retrieval portal: #{input_data['retrieval_portal']}"
      puts "🔸 Found #{property_urls.size} property URL(s)"

      realty_game_id = create_initial_game_with_pre_scraped(input_data, property_urls.first)
      add_remaining_pre_scraped_listings(input_data, property_urls.drop(1), realty_game_id)

      puts "🎯 All pre-scraped listings processed. Realty game ID: #{realty_game_id}"
      realty_game_id
    end

    def create_dossier_from_url(retrieval_end_point, retrieval_portal)
      response = make_post_request(
        "#{@api_prefix}/dossiers_mgmt/dossier_from_url",
        {
          retrieval_end_point: retrieval_end_point,
          retrieval_portal: retrieval_portal
        }
      )
      JSON.parse(response.body)['realty_dossier']['id']
    end

    def add_asset_to_dossier(dossier_id, retrieval_end_point, retrieval_portal)
      response = make_put_request(
        "#{@api_prefix}/dossiers_mgmt/add_asset_from_url",
        {
          dossier_id: dossier_id,
          retrieval_end_point: retrieval_end_point,
          retrieval_portal: retrieval_portal
        }
      )
      JSON.parse(response.body)
    end

    private

    def read_and_validate_input(input_file)
      unless File.exist?(input_file)
        puts "❌ Input file not found: #{input_file}"
        @logger.error("Input file not found: #{input_file}")
        raise "Input file not found: #{input_file}"
      end

      puts "📂 Reading input file: #{input_file}"
      input_data = JSON.parse(File.read(input_file))
      input_data['api_prefix'] ||= 'https://be-medo.propertywebbuilder.com/api_mgmt/v4'
      input_data
    end

    def validate_property_urls(property_urls)
      return unless property_urls.empty?

      puts '❌ No property URLs found in input file'
      @logger.error('No property URLs found in input file')
      raise 'No property URLs found in input file'
    end

    def determine_retrieval_portal_from_url(url)
      case url
      when /buenavistahomes\.eu/
        'buenavista'
      when /onthemarket\.com/
        'onthemarket'
      when /zoopla\.co\.uk/
        'zoopla'
      when /rightmove\.co\.uk/
        'rightmove'
      when /purplebricks\.co\.uk/
        'purplebricks'
      else
        raise "Unknown retrieval portal for URL: #{url}"
      end
    end

    def make_post_request(url, body)
      response = HTTParty.post(
        url,
        body: body.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        }
      )
      validate_response(response, "Failed to perform POST request to #{url}")
      response
    end

    def make_put_request(url, body)
      response = HTTParty.put(
        url,
        body: body.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        }
      )
      validate_response(response, "Failed to perform PUT request to #{url}")
      response
    end

    def validate_response(response, error_message)
      return if response.success?

      puts "❌ #{error_message}: #{response.body}"
      @logger.error("#{error_message}: #{response.body}")
      raise "#{error_message}: #{response.body}"
    end

    def create_initial_game(input_data, first_url)
      retrieval_portal = determine_retrieval_portal_from_url(first_url)
      puts "🚀 Creating initial game with first listing: #{first_url} (Portal: #{retrieval_portal})"

      init_response = make_post_request(
        "#{input_data['api_prefix']}/realty_games_mgmt/init_game_with_listing",
        {
          realty_game_slug: input_data['realty_game_slug'] || 'regular-game',
          vendor_name: input_data['vendor_name'],
          game_title: input_data['game_title'],
          game_description: input_data['game_description'],
          game_bg_image_url: input_data['background_image'],
          scoot_subdomain: input_data['scoot_subdomain'],
          retrieval_portal: retrieval_portal,
          retrieval_end_point: first_url
        }
      )

      realty_game_id = JSON.parse(init_response.body)['realty_game_id'] # Adjust based on actual response
      @logger.info("Created realty game ID: #{realty_game_id} with first listing")
      realty_game_id
    end

    def add_remaining_listings(input_data, property_urls, realty_game_id)
      puts '⏳ Sleeping for 30 seconds before adding more listings...'
      sleep 30

      property_urls.each_with_index do |url, index|
        retrieval_portal = determine_retrieval_portal_from_url(url)
        puts "➕ Adding listing #{index + 2}/#{property_urls.size + 1}: #{url} (Portal: #{retrieval_portal})"

        response = make_post_request(
          "#{input_data['api_prefix']}/realty_games_mgmt/add_listing_to_game",
          {
            realty_game_id: realty_game_id,
            retrieval_portal: retrieval_portal,
            retrieval_end_point: url
          }
        )

        puts '✅ Successfully added listing'
        @logger.info("Added listing for: #{retrieval_end_point}")
        puts '⏳ Sleeping for another 30 seconds...'
        sleep 30
      end
    end

    def create_initial_game_with_pre_scraped(input_data, first_url)
      retrieval_portal = determine_retrieval_portal_from_url(first_url)
      puts "🚀 Pre-scraping and creating initial game with first listing: #{first_url} (Portal: #{retrieval_portal})"

      # Pre-scrape the content locally
      first_scrape_item_data = create_and_serialize_scrape_item(first_url, retrieval_portal)

      init_response = make_post_request(
        "#{input_data['api_prefix']}/realty_games_mgmt/init_game_with_pre_scraped_listing",
        {
          realty_game_slug: input_data['realty_game_slug'] || 'regular-game',
          game_title: input_data['game_title'],
          game_description: input_data['game_description'],
          game_bg_image_url: input_data['background_image'],
          vendor_name: input_data['vendor_name'],
          scoot_subdomain: input_data['scoot_subdomain'],
          retrieval_portal: retrieval_portal,
          retrieval_end_point: first_url,
          scrape_item_data: first_scrape_item_data
        }
      )

      realty_game_id = JSON.parse(init_response.body)['realty_game_id']
      @logger.info("Created realty game ID: #{realty_game_id} with first pre-scraped listing")
      realty_game_id
    end

    def add_remaining_pre_scraped_listings(input_data, property_urls, realty_game_id)
      puts '⏳ Sleeping for 30 seconds before adding more listings...'
      sleep 30

      property_urls.each_with_index do |url, index|
        retrieval_portal = determine_retrieval_portal_from_url(url)
        puts "➕ Pre-scraping and adding listing #{index + 2}/#{property_urls.size + 1}: #{url} (Portal: #{retrieval_portal})"

        # Pre-scrape the content locally
        scrape_item_data = create_and_serialize_scrape_item(url, retrieval_portal)

        response = make_post_request(
          "#{input_data['api_prefix']}/realty_games_mgmt/add_pre_scraped_listing_to_game",
          {
            realty_game_id: realty_game_id,
            retrieval_portal: retrieval_portal,
            retrieval_end_point: url,
            scrape_item_data: scrape_item_data
          }
        )

        puts '✅ Successfully added pre-scraped listing'
        @logger.info("Added pre-scraped listing for: #{retrieval_end_point}")
        puts '⏳ Sleeping for another 30 seconds...'
        sleep 30
      end
    end

    def create_and_serialize_scrape_item(url, portal)
      scrape_item = create_scrape_item_locally(url, portal)
      serialize_scrape_item(scrape_item)
    end

    # Creates a scrape item locally using the same logic as RealtyGameListingCreator
    def create_scrape_item_locally(url, portal)
      puts "🔍 Creating scrape item locally for: #{url}"

      # Use the same portal configuration as RealtyGameListingCreator
      portal_config = {
        'buenavista' => {
          scrape_class: 'ScrapeItemFromBuenavista',
          connector: 'ScraperConnectors::Json',
          method: :find_or_create_for_h2c_buenavista,
          include_trailing_slash: false
        },
        'onthemarket' => {
          scrape_class: 'ScrapeItemFromOtm',
          connector: 'ScraperConnectors::Regular',
          method: :find_or_create_for_h2c_onthemarket,
          include_trailing_slash: true
        },
        'zoopla' => {
          scrape_class: 'ScrapeItem',
          connector: 'ScraperConnectors::LocalPlaywright',
          method: :find_or_create_for_h2c,
          include_trailing_slash: false
        },
        'rightmove' => {
          scrape_class: 'ScrapeItemFromRightmove',
          connector: 'ScraperConnectors::LocalPlaywright',
          method: :find_or_create_for_h2c_rightmove,
          include_trailing_slash: false
        },
        'purplebricks' => {
          scrape_class: 'ScrapeItemFromPurplebricks',
          connector: 'ScraperConnectors::Purplebricks',
          method: :find_or_create_for_h2c_purplebricks,
          include_trailing_slash: false
        }
      }

      p_config = portal_config[portal]
      raise "Unknown portal: #{portal}" unless p_config

      scrape_class = p_config[:scrape_class].constantize
      scrape_item = scrape_class.send(p_config[:method], url)
      scrape_item.retrieve_and_set_content_object(
        p_config[:connector],
        include_trailing_slash: p_config[:include_trailing_slash],
        force_retrieval: false
      )

      puts '✅ Successfully created scrape item locally'
      scrape_item
    end

    # Serializes a scrape item to a hash that can be sent via JSON
    def serialize_scrape_item(scrape_item)
      {
        scrape_class: scrape_item.class.name,
        scrapable_url: scrape_item.scrapable_url,
        scrape_unique_url: scrape_item.scrape_unique_url,
        full_content_before_js: scrape_item.full_content_before_js,
        full_content_after_js: scrape_item.full_content_after_js,
        title: scrape_item.title,
        description: scrape_item.description,
        page_locale_code: scrape_item.page_locale_code,
        is_valid_scrape: scrape_item.is_valid_scrape,
        content_is_html: scrape_item.content_is_html,
        content_is_json: scrape_item.content_is_json,
        content_is_xml: scrape_item.content_is_xml,
        all_page_images: scrape_item.all_page_images,
        script_json: begin
          json_data = scrape_item.script_json
          json_data.is_a?(String) ? JSON.parse(json_data) : json_data
        rescue JSON::ParserError
          nil # Handle invalid JSON strings gracefully
        end,
        # Portal-specific flags
        scrape_is_buenavista: scrape_item.respond_to?(:scrape_is_buenavista) ? scrape_item.scrape_is_buenavista : false,
        scrape_is_onthemarket: scrape_item.respond_to?(:scrape_is_onthemarket) ? scrape_item.scrape_is_onthemarket : false,
        scrape_is_zoopla: scrape_item.respond_to?(:scrape_is_zoopla) ? scrape_item.scrape_is_zoopla : false,
        scrape_is_rightmove: scrape_item.respond_to?(:scrape_is_rightmove) ? scrape_item.scrape_is_rightmove : false,
        scrape_is_purplebricks: scrape_item.respond_to?(:scrape_is_purplebricks) ? scrape_item.scrape_is_purplebricks : false
      }
    end
  end
end
