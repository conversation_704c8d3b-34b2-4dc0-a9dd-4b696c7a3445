class ApiPublic::V4::GameListingsController < ApplicationController
  skip_before_action :verify_authenticity_token
  respond_to :json

  def show_for_sale
    @sale_listing = SaleListing.find_by_uuid(params[:sale_listing_uuid])
    if @sale_listing.nil?
      render json: { error: 'Listing not found' }, status: :not_found
    else
      render 'api_public/v4/game_listings/show_for_sale'
    end
    # file_path = Rails.root.join('app/views/', 'api_public/v4/purchase_evaluations/show.json')
    # render json: File.read(file_path), status: :ok
  end

  # def list
  #   @sale_listings = SaleListing.kept.order('created_at desc')
  #   render 'api_public/v4/sale_listings/list'
  # end
end
