module ApiMgmt::V4
  class RealtyGamesMgmtController < ApplicationController
    skip_before_action :verify_authenticity_token

    def clear_cache
      # unless request.headers['Authorization'] == "Bearer #{ENV['CACHE_CLEAR_TOKEN']}"
      #   render json: { error: 'Unauthorized' }, status: :unauthorized
      #   return
      # end

      unless params['clear_cache_code'] == '0505'
        render json: { error: 'Unauthorized' }, status: :unauthorized
        return
      end

      begin
        Rails.cache.clear
        # # Clear specific caches
        # Rails.cache.delete_matched('show_games/*')
        # Rails.cache.delete_matched('price_guess_inputs/*')
        render json: { message: 'Specific caches cleared successfully' }, status: :ok
      rescue StandardError => e
        render json: { error: "Failed to clear cache: #{e.message}" }, status: :internal_server_error
      end
      # might also need to do below
      # curl -X POST "https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID/purge_cache" \
      # -H "Authorization: Bearer YOUR_API_TOKEN" \
      # -H "Content-Type: application/json" \
      # --data '{"purge_everything":true}'
    end

    def show_available_games
      # 22 june 2025 - started this for getManagementGames
      # in scoot-management fe route
      realty_games = RealtyGame.kept.all
      render json: { available_realty_games: realty_games.as_json(
        only: %w[
          uuid game_source_portal
          realty_game_slug guessed_prices_count game_sessions_count
          game_jots_count game_listings_count game_start_at game_end_at
          game_bg_image_url
          game_starting_url game_title game_description
          game_default_currency game_default_country game_default_locale
        ],
        methods: %w[is_hidden_from_landing_page]
      ) }
    end

    # Endpoint called by realty_punt_creator - allows me
    # to create game on prod server from my dev env
    def init_game_with_listing
      vendor_name = params[:vendor_name]
      scoot_subdomain = params[:scoot_subdomain]
      retrieval_portal = params[:retrieval_portal]
      retrieval_end_point = params[:retrieval_end_point]
      realty_game_slug = params[:realty_game_slug] || 'regular-game'

      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

      scoot = Scoot.find_or_create_by!(scoot_subdomain: scoot_subdomain)
      scoot.update!(
        supports_multiple_games: true,
        should_show_out_links: true
      )

      realty_game = scoot.realty_games.find_or_create_by!(
        realty_game_slug: realty_game_slug
      )
      realty_game.update!(
        game_source_portal: vendor_name,
        game_title: 'Regular Game',
        game_description: 'A regular game to validate the RealtyGameListingCreator service',
        game_default_currency: 'EUR',
        game_default_country: 'UK',
        game_default_locale: 'en',
        game_start_at: 1.hour.ago,
        game_end_at: 1.week.from_now
      )

      realty_game.add_listing_from_url(retrieval_end_point, retrieval_portal)

      render json: { realty_game_id: realty_game.id }, status: :ok
    rescue StandardError => e
      message_to_return = "First backtrace #{e.backtrace.first} Failed to init game: #{e.message}"
      logger.error("Failed to init game: #{e.message}")
      logger.error("First backtrace #{e.backtrace.first}")

      render json: { error: message_to_return }, status: :internal_server_error
    end

    def add_listing_to_game
      realty_game_id = params[:realty_game_id]
      retrieval_portal = params[:retrieval_portal]
      retrieval_end_point = params[:retrieval_end_point]

      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

      realty_game = RealtyGame.find(realty_game_id)
      realty_game.add_listing_from_url(retrieval_end_point, retrieval_portal)

      render json: { message: 'Listing added' }, status: :ok
    rescue StandardError => e
      logger.error("Failed to add listing: #{e.message}")
      render json: { error: e.message }, status: :internal_server_error
    end

    # New endpoint that creates a game with pre-scraped content
    # This avoids the need for the server to scrape content from the retrieval_end_point
    def init_game_with_pre_scraped_listing
      vendor_name = params[:vendor_name]
      scoot_subdomain = params[:scoot_subdomain]
      retrieval_portal = params[:retrieval_portal]
      retrieval_end_point = params[:retrieval_end_point]
      game_bg_image_url = params[:game_bg_image_url]
      game_title = params[:game_title] || 'Price Guessing Game'
      game_description = params[:game_description] || 'Price Guessing Game'
      realty_game_slug = params[:realty_game_slug] || 'regular-game'
      realty_game_slug = game_title.parameterize if game_title
      scrape_item_data = params[:scrape_item_data]

      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

      scoot = Scoot.find_or_create_by!(scoot_subdomain: scoot_subdomain)
      scoot.update!(
        supports_multiple_games: true,
        should_show_out_links: true
      )

      realty_game = scoot.realty_games.find_or_create_by!(
        realty_game_slug: realty_game_slug
      )
      #      game_bg_image_url: 'https://upload.wikimedia.org/wikipedia/commons/3/31/Sheffield_City.jpg',
      # game_title: rg3_title,
      # game_description: rg3_description,
      # game_default_locale: rg3_title.parameterize

      realty_game.update!(
        game_source_portal: vendor_name,
        game_title: game_title,
        game_description: game_description,
        game_bg_image_url: game_bg_image_url,
        #  'A regular game to validate the RealtyGameListingCreator service',
        game_default_currency: 'EUR',
        game_default_country: 'UK',
        # currently game_default_locale is serving as a workaround
        # for a global game slug
        game_default_locale: realty_game_slug,
        game_start_at: 1.hour.ago,
        game_end_at: 1.week.from_now
      )

      # Will have to figure out how to update realty_game_ids on scoot
      # hpg_scoot.update(
      #   realty_game_ids: [2, 3]
      # )
      # Create listing from pre-scraped content
      realty_game.add_listing_from_pre_scraped_content(retrieval_end_point, retrieval_portal, scrape_item_data)

      render json: { realty_game_id: realty_game.id }, status: :ok
    rescue StandardError => e
      message_to_return = "First backtrace #{e.backtrace.first} Failed to init game with pre-scraped content: #{e.message}"
      logger.error("Failed to init game with pre-scraped content: #{e.message}")
      logger.error("First backtrace #{e.backtrace.first}")

      render json: { error: message_to_return }, status: :internal_server_error
    end

    # New endpoint that adds a listing to an existing game using pre-scraped content
    def add_pre_scraped_listing_to_game
      realty_game_id = params[:realty_game_id]
      retrieval_portal = params[:retrieval_portal]
      retrieval_end_point = params[:retrieval_end_point]
      scrape_item_data = params[:scrape_item_data]

      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

      realty_game = RealtyGame.find(realty_game_id)
      realty_game.add_listing_from_pre_scraped_content(retrieval_end_point, retrieval_portal, scrape_item_data)

      render json: { message: 'Pre-scraped listing added' }, status: :ok
    rescue StandardError => e
      logger.error("Failed to add pre-scraped listing: #{e.message}")
      render json: { error: e.message }, status: :internal_server_error
    end

    # def create_game
    #   ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
    #   set_scoot
    #   realty_game = Scoot.last.realty_games.create

    #   # Step 2: Create a RealtyGame (if needed)
    #   realty_game = @scoot.realty_games.create!(
    #     # Add required fields for RealtyGame, e.g., title, status
    #     game_title: "Game for #{@scoot.scoot_title}"
    #   )

    #   # Step 3: Link via RealtyGameListing
    #   SaleListing.limit(3).each do |sale_listing|
    #     _realty_game_listing = @scoot.realty_game_listings.create!(
    #       sale_listing: sale_listing,
    #       # realty_game: realty_game,
    #       scoot_uuid: @scoot.uuid
    #     )
    #   end

    #   render json: { realty_game: realty_game }, status: :ok
    # end

    def listing_visibility
      @listing = SaleListing.find_by_uuid(
        params[:listing_uuid]
      )
      if @listing
        @listing.visible = params[:sale_listing][:visible]
        @listing.save!
        render json: { listing: @listing }, status: :ok
      else
        render json: { error: 'No listing found' }, status: :not_found
      end
    rescue StandardError => e
      Rails.logger.error("Error in listing_visibility: #{e.message}")
      render json: { error: 'Internal server error' }, status: :internal_server_error
    end

    def photo_visibility
      @listing_photo = RealtyAssetPhoto.find_by_uuid(
        params[:photo_uuid]
      )
      if @listing_photo
        @listing_photo.flag_is_hidden = params[:sale_listing_pic][:flag_is_hidden]
        @listing_photo.save!
        render json: { listing_photo: @listing_photo }, status: :ok
      else
        render json: { error: 'No listing_photo found' }, status: :not_found
      end
    rescue StandardError => e
      Rails.logger.error("Error in listing_photo_visibility: #{e.message}")
      render json: { error: 'Internal server error' }, status: :internal_server_error
    end

    private

    def set_scoot
      incoming_subdomain = request.subdomain.presence || 'default'
      @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)

      # Find the scoot by its UUID from the URL parameter
      # @scoot = Scoot.find_by(uuid: params[:scoot_uuid])

      # OR, if you are scoping to the current user:
      # @scoot = current_user.scoots.find_by(uuid: params[:uuid])
      # OR, if a user has only one scoot:
      # @scoot = current_user.scoot
      # Ensure @scoot is found, otherwise return a 404
      return if @scoot

      render json: { error: 'Scoot not found' }, status: :not_found
    end
  end
end
