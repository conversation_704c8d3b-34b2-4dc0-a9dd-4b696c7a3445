# == Schema Information
#
# Table name: realty_game_listings
#
#  id                          :bigint           not null, primary key
#  average_guess_cents         :bigint
#  discarded_at                :datetime
#  game_sessions_count         :integer          default(0)
#  guessed_prices_count        :integer          default(0)
#  highest_guess_cents         :bigint
#  is_rental_listing           :boolean          default(FALSE)
#  is_sale_listing             :boolean          default(TRUE)
#  listing_uuid                :uuid
#  lowest_guess_cents          :bigint
#  realty_asset_uuid           :uuid
#  realty_game_listing_details :jsonb
#  realty_game_listing_flags   :integer          default(0), not null
#  realty_game_uuid            :uuid
#  scoot_uuid                  :uuid
#  statistics_updated_at       :datetime
#  total_guesses_count         :integer          default(0)
#  translations                :jsonb
#  uuid                        :uuid
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#
# Indexes
#
#  index_realty_game_listings_on_average_guess_cents        (average_guess_cents)
#  index_realty_game_listings_on_discarded_at               (discarded_at)
#  index_realty_game_listings_on_listing_uuid               (listing_uuid)
#  index_realty_game_listings_on_realty_game_listing_flags  (realty_game_listing_flags)
#  index_realty_game_listings_on_realty_game_uuid           (realty_game_uuid)
#  index_realty_game_listings_on_statistics_updated_at      (statistics_updated_at)
#  index_realty_game_listings_on_uuid                       (uuid)
#
class RealtyGameListing < ApplicationRecord
  include Discard::Model

  # Relationships
  # Realised after a while that game_sessions_count might be nonsense
  # as there can't be a direct relation between a game_session
  # has_many :game_sessions, primary_key: 'uuid', foreign_key: 'realty_game_listing_uuid', dependent: :destroy
  has_many :game_sessions, through: :guessed_prices, source: :game_session
  has_many :guessed_prices, primary_key: 'uuid', foreign_key: 'realty_game_listing_uuid', dependent: :destroy

  belongs_to :scoot, class_name: 'Scoot', foreign_key: 'scoot_uuid',
                     primary_key: :uuid, optional: true
  belongs_to :realty_game, class_name: 'RealtyGame', foreign_key: 'realty_game_uuid',
                           primary_key: :uuid, optional: true
  counter_culture :realty_game, column_name: 'game_listings_count'
  belongs_to :realty_asset, class_name: 'RealtyAsset', foreign_key: 'realty_asset_uuid',
                            primary_key: :uuid, optional: true
  belongs_to :sale_listing, class_name: 'SaleListing', foreign_key: 'listing_uuid',
                            primary_key: :uuid, optional: true
  belongs_to :rental_listing, class_name: 'RentalListing', foreign_key: 'listing_uuid',
                              primary_key: :uuid, optional: true

  # Polymorphic association for listings - can be either SaleListing or RentalListing
  def listing
    return nil if listing_uuid.blank?

    if is_sale_listing?
      SaleListing.find_by(uuid: listing_uuid)
    elsif is_rental_listing?
      RentalListing.find_by(uuid: listing_uuid)
    end
  end

  def listing_display_url
    listing.listing_display_url
  end

  def source_listing_currency
    listing.currency
  end

  # Validations
  validates :uuid, presence: true, uniqueness: true
  # validates :realty_game_uuid, presence: true
  validates :listing_uuid, presence: true
  validate :listing_type_consistency

  # Scopes
  scope :for_sale, -> { where(is_sale_listing: true) }
  scope :for_rental, -> { where(is_rental_listing: true) }
  scope :with_statistics, -> { where.not(statistics_updated_at: nil) }

  # Callbacks
  before_validation :ensure_uuid
  before_validation :set_listing_type

  # Statistics methods
  def statistics_available?
    statistics_updated_at.present?
  end

  def formatted_average_guess
    return nil unless average_guess_cents.present?

    currency = listing&.price_sale_current_currency || 'GBP'
    Money.from_cents(average_guess_cents, currency).format(no_cents: true)
  end

  def formatted_highest_guess
    return nil unless highest_guess_cents.present?

    currency = listing&.price_sale_current_currency || 'GBP'
    Money.from_cents(highest_guess_cents, currency).format(no_cents: true)
  end

  def formatted_lowest_guess
    return nil unless lowest_guess_cents.present?

    currency = listing&.price_sale_current_currency || 'GBP'
    Money.from_cents(lowest_guess_cents, currency).format(no_cents: true)
  end

  def guess_range_cents
    return nil unless highest_guess_cents.present? && lowest_guess_cents.present?

    highest_guess_cents - lowest_guess_cents
  end

  def formatted_guess_range
    return nil unless guess_range_cents.present?

    currency = listing&.price_sale_current_currency || 'GBP'
    Money.from_cents(guess_range_cents, currency).format(no_cents: true)
  end

  def statistics_summary
    return nil unless statistics_available?

    {
      average_guess_cents: average_guess_cents,
      average_guess_formatted: formatted_average_guess,
      highest_guess_cents: highest_guess_cents,
      highest_guess_formatted: formatted_highest_guess,
      lowest_guess_cents: lowest_guess_cents,
      lowest_guess_formatted: formatted_lowest_guess,
      guess_range_cents: guess_range_cents,
      guess_range_formatted: formatted_guess_range,
      total_guesses_count: total_guesses_count,
      statistics_updated_at: statistics_updated_at
    }
  end

  private

  def ensure_uuid
    self.uuid ||= SecureRandom.uuid
  end

  def set_listing_type
    # Ensure only one listing type is true
    if is_sale_listing? && is_rental_listing?
      self.is_rental_listing = false
    elsif !is_sale_listing? && !is_rental_listing?
      self.is_sale_listing = true # Default to sale listing
    end
  end

  def listing_type_consistency
    errors.add(:base, 'Must be either a sale listing or rental listing') if !is_sale_listing? && !is_rental_listing?

    return unless is_sale_listing? && is_rental_listing?

    errors.add(:base, 'Cannot be both sale and rental listing')
  end
end
