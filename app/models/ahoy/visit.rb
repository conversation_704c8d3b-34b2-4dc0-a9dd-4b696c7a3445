# == Schema Information
#
# Table name: ahoy_visits
#
#  id               :bigint           not null, primary key
#  app_version      :string
#  browser          :string
#  city             :string
#  country          :string
#  device_type      :string
#  ip               :string
#  landing_page     :text
#  latitude         :float
#  longitude        :float
#  os               :string
#  os_version       :string
#  platform         :string
#  referrer         :text
#  referring_domain :string
#  region           :string
#  started_at       :datetime
#  user_agent       :text
#  utm_campaign     :string
#  utm_content      :string
#  utm_medium       :string
#  utm_source       :string
#  utm_term         :string
#  visit_token      :string
#  visitor_token    :string
#  user_id          :bigint
#
# Indexes
#
#  index_ahoy_visits_on_user_id      (user_id)
#  index_ahoy_visits_on_visit_token  (visit_token) UNIQUE
#
class Ahoy::Visit < ApplicationRecord
  self.table_name = 'ahoy_visits'

  has_many :events, class_name: 'Ahoy::Event'
  has_many :game_sessions, primary_key: 'visitor_token', foreign_key: 'site_visitor_token',
                           class_name: 'GameSession'
  belongs_to :participant, primary_key: :visitor_token, foreign_key: :visitor_token, optional: true

  belongs_to :user, optional: true

  # Callback to sync participant data when visit is created/updated
  after_create :sync_participant_data
  after_update :sync_participant_data, if: :saved_change_to_visitor_token?

  private

  def sync_participant_data
    return unless visitor_token.present?

    participant = Participant.find_or_initialize_by(visitor_token: visitor_token)
    participant.sync_with_visit(self)
    participant.save!
  end
end
