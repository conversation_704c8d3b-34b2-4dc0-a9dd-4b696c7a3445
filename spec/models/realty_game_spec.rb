# == Schema Information
#
# Table name: realty_games
#
#  id                     :bigint           not null, primary key
#  agency_tenant_uuid     :uuid             not null
#  discarded_at           :datetime
#  game_area_details      :jsonb
#  game_bg_image_url      :string
#  game_default_country   :string
#  game_default_currency  :string           default("GBP"), not null
#  game_default_locale    :string
#  game_description       :string
#  game_end_at            :datetime
#  game_jots_count        :integer          default(0)
#  game_listings_count    :integer          default(0)
#  game_notes             :jsonb
#  game_primary_user_uuid :uuid
#  game_rules             :jsonb
#  game_sessions_count    :integer          default(0)
#  game_settings_flags    :integer          default(0), not null
#  game_source_portal     :integer          default("vendor_missing"), not null
#  game_start_at          :datetime
#  game_starting_url      :string
#  game_title             :string
#  game_type_flags        :integer          default(0), not null
#  guessed_prices_count   :integer          default(0)
#  realty_game_aasm_state :string
#  realty_game_details    :jsonb
#  realty_game_flags      :integer          default(0), not null
#  realty_game_slug       :string
#  scoot_uuid             :uuid
#  translations           :jsonb
#  uuid                   :uuid
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_realty_games_on_discarded_at         (discarded_at)
#  index_realty_games_on_game_settings_flags  (game_settings_flags)
#  index_realty_games_on_game_type_flags      (game_type_flags)
#  index_realty_games_on_realty_game_flags    (realty_game_flags)
#  index_realty_games_on_realty_game_slug     (realty_game_slug)
#  index_realty_games_on_scoot_uuid           (scoot_uuid)
#  index_realty_games_on_uuid                 (uuid)
#
require 'rails_helper'

RSpec.describe RealtyGame, type: :model do
  pending "add some examples to (or delete) #{__FILE__}"
end
