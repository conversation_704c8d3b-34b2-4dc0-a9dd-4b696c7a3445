# == Schema Information
#
# Table name: game_sessions
#
#  id                         :bigint           not null, primary key
#  agency_tenant_uuid         :uuid
#  discarded_at               :datetime
#  game_session_details       :jsonb
#  game_session_flags         :integer          default(0), not null
#  guessed_prices_count       :integer          default(0)
#  is_protected               :boolean          default(FALSE), not null
#  main_realty_game_uuid      :uuid
#  main_scoot_uuid            :uuid
#  max_possible_score         :integer
#  performance_percentage     :decimal(5, 2)
#  performance_rating_color   :string
#  performance_rating_icon    :string
#  performance_rating_text    :string
#  results_calculated_at      :datetime
#  session_guest_name         :string
#  session_guest_title        :string
#  session_preferred_currency :string           default("GBP"), not null
#  site_visitor_token         :string
#  total_score                :integer
#  uuid                       :uuid             not null
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
# Indexes
#
#  index_game_sessions_on_agency_tenant_uuid     (agency_tenant_uuid)
#  index_game_sessions_on_discarded_at           (discarded_at)
#  index_game_sessions_on_game_session_flags     (game_session_flags)
#  index_game_sessions_on_main_realty_game_uuid  (main_realty_game_uuid)
#  index_game_sessions_on_main_scoot_uuid        (main_scoot_uuid)
#  index_game_sessions_on_results_calculated_at  (results_calculated_at)
#  index_game_sessions_on_site_visitor_token     (site_visitor_token)
#  index_game_sessions_on_total_score            (total_score)
#  index_game_sessions_on_uuid                   (uuid) UNIQUE
#
FactoryBot.define do
  factory :game_session do
    
  end
end
