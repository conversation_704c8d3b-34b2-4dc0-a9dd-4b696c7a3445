class AddStatisticsToRealtyGameListings < ActiveRecord::Migration[7.2]
  def change
    add_column :realty_game_listings, :average_guess_cents, :bigint
    add_column :realty_game_listings, :highest_guess_cents, :bigint
    add_column :realty_game_listings, :lowest_guess_cents, :bigint
    add_column :realty_game_listings, :total_guesses_count, :integer, default: 0
    add_column :realty_game_listings, :statistics_updated_at, :datetime

    add_index :realty_game_listings, :statistics_updated_at
    add_index :realty_game_listings, :average_guess_cents
  end
end
