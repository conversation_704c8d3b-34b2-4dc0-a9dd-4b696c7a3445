namespace :h2c do
  desc 'Start realty_game from url with input from file'
  task start_realty_game_from_url_generic_v2: :environment do
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    input_file = 'db/realty_punts/latest.json'
    service = RealtyPunts::RealtyPuntCreator.new
    input_data = JSON.parse(File.read(input_file))
    result = if input_data['pre_scrape']
               service.create_realty_game_with_pre_scraped_content(input_file)
             else
               service.create_realty_game(input_file)
             end

    puts "game with id: #{result} created"
    # puts "result: #{result}"
  end
end
